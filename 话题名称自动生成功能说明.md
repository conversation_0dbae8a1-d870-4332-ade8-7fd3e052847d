# 话题名称自动生成功能说明

## 功能概述

本次更新为 Yidev.LocalAI 项目添加了自动根据用户提问内容生成话题名称的功能。当用户在新话题中发送第一条消息时，系统会自动调用AI模型生成一个简洁、准确的话题标题，替换默认的"新话题 + 时间戳"格式。

## 实现细节

### 1. 新增方法

#### SemanticKernelService.GenerateTopicTitleAsync()
- **位置**: `Services/SemanticKernelService.cs`
- **功能**: 根据用户消息生成简洁的话题标题
- **特点**:
  - 使用独立的ChatHistory，不影响主对话
  - 限制标题长度不超过15个字符
  - 设置较低的Temperature(0.3)确保生成稳定
  - 包含异常处理和回退机制

```csharp
public async Task<string> GenerateTopicTitleAsync(string userMessage)
```

### 2. 修改的方法

#### MainViewModel.SendMessage()
- **位置**: `Models/MainViewModel.cs`
- **修改内容**:
  - 添加了检测是否为新话题第一条消息的逻辑
  - 在发送第一条消息时自动生成并更新话题标题
  - 包含异常处理，确保即使生成失败也有回退方案

## 工作流程

1. **用户创建新话题**: 系统创建默认名称为"新话题 + 时间戳"的话题
2. **用户发送第一条消息**: 
   - 系统检测到这是新话题的第一条消息
   - 调用AI模型生成话题标题
   - 更新话题名称到数据库
   - 继续正常的消息处理流程
3. **后续消息**: 不再触发标题生成，正常处理

## 回退机制

如果AI生成标题失败，系统会：
1. 使用用户消息的前15个字符作为标题
2. 如果消息超过15个字符，会添加"..."后缀
3. 确保话题始终有一个有意义的名称

## 技术特点

- **非阻塞**: 标题生成不会影响正常的消息发送流程
- **容错性**: 包含完整的异常处理机制
- **性能优化**: 只在第一条消息时触发，避免不必要的API调用
- **用户体验**: 自动生成，无需用户手动操作

## 示例效果

| 用户消息 | 生成的标题 |
|---------|-----------|
| "你好，请介绍一下你自己" | "自我介绍" |
| "如何学习编程？" | "编程学习" |
| "什么是人工智能？" | "人工智能概念" |
| "请帮我写一个Python函数" | "Python函数编写" |

## 配置说明

标题生成使用以下AI模型设置：
- **MaxTokens**: 50（限制生成长度）
- **Temperature**: 0.3（确保生成稳定性）
- **模型**: 与主对话相同的模型（gemini-2.5-pro-preview-06-05）

## 测试

项目中包含了 `TestTopicTitleGeneration.cs` 文件，可以用于测试标题生成功能。

## 注意事项

1. 需要确保AI服务正常运行
2. 标题生成依赖网络连接
3. 如果AI服务不可用，会自动使用回退方案
4. 生成的标题会自动保存到数据库中

## 修复的问题

### 双窗口启动问题
**问题**: 应用程序启动时会打开两个主窗口
**原因**:
- `App.xaml` 中设置了 `StartupUri="MainWindow.xaml"`
- `App.xaml.cs` 中的 `OnStartup` 方法又通过依赖注入获取并显示了另一个 MainWindow

**解决方案**: 移除了 `App.xaml` 中的 `StartupUri` 属性，只保留依赖注入方式启动窗口

**修改文件**: `App.xaml`
```xml
<!-- 修改前 -->
<Application StartupUri="MainWindow.xaml">

<!-- 修改后 -->
<Application>
```

现在应用程序启动时只会显示一个主窗口。
