﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace Yidev.LocalAI.Models
{
    public class ChatMessage
    {
        public int Id { get; set; }
        public string Role { get; set; }
        public string Content { get; set; }
        public DateTime Timestamp { get; set; }
        public int TopicId { get; set; }
        public Topic Topic { get; set; }
        public string? SenderName { get; set; }
        [NotMapped]
        public string Avatar { get; set; }
    }
}
