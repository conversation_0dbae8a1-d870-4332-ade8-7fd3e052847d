2025-07-09 09:23:52.625 +08:00 [DBG] Hosting starting
2025-07-09 09:23:52.686 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-09 09:23:52.692 +08:00 [INF] Hosting environment: Production
2025-07-09 09:23:52.695 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-09 09:23:52.696 +08:00 [DBG] Hosting started
2025-07-09 09:23:52.698 +08:00 [INF] Application Starting Up
2025-07-09 09:23:53.666 +08:00 [DBG] warn: 2025/7/9 09:23:53.665 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-09 09:23:53.836 +08:00 [DBG] info: 2025/7/9 09:23:53.836 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-09 09:23:53.842 +08:00 [INF] TopicService initialized and database ensured.
2025-07-09 09:23:54.018 +08:00 [INF] Initializing SemanticKernelService...
2025-07-09 09:23:54.036 +08:00 [INF] SemanticKernelService initialized.
2025-07-09 09:23:56.926 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-09 09:23:56.928 +08:00 [WRN] ServiceProvider 为 null，无法添加自定义插件
2025-07-09 09:23:56.933 +08:00 [INF] Getting topics for user: llk
2025-07-09 09:23:57.432 +08:00 [DBG] info: 2025/7/9 09:23:57.432 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-09 09:23:57.491 +08:00 [INF] Getting messages for topic ID: 1
2025-07-09 09:23:57.507 +08:00 [DBG] info: 2025/7/9 09:23:57.507 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-09 09:23:57.531 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-09 09:23:57.532 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-09 09:26:01.574 +08:00 [DBG] Hosting starting
2025-07-09 09:26:01.637 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-09 09:26:01.643 +08:00 [INF] Hosting environment: Production
2025-07-09 09:26:01.646 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-09 09:26:01.647 +08:00 [DBG] Hosting started
2025-07-09 09:26:01.648 +08:00 [INF] Application Starting Up
2025-07-09 09:26:02.633 +08:00 [DBG] warn: 2025/7/9 09:26:02.633 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-09 09:26:02.800 +08:00 [DBG] info: 2025/7/9 09:26:02.800 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (13ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-09 09:26:02.806 +08:00 [INF] TopicService initialized and database ensured.
2025-07-09 09:26:02.988 +08:00 [INF] Initializing SemanticKernelService...
2025-07-09 09:26:03.008 +08:00 [INF] SemanticKernelService initialized.
2025-07-09 09:26:06.567 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-09 09:26:06.572 +08:00 [INF] Getting topics for user: llk
2025-07-09 09:26:07.078 +08:00 [DBG] info: 2025/7/9 09:26:07.078 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-09 09:26:07.139 +08:00 [INF] Getting messages for topic ID: 1
2025-07-09 09:26:07.155 +08:00 [DBG] info: 2025/7/9 09:26:07.155 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-09 09:26:07.179 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-09 09:26:07.180 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-09 09:26:47.548 +08:00 [DBG] Hosting starting
2025-07-09 09:26:47.610 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-09 09:26:47.617 +08:00 [INF] Hosting environment: Production
2025-07-09 09:26:47.619 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-09 09:26:47.620 +08:00 [DBG] Hosting started
2025-07-09 09:26:47.622 +08:00 [INF] Application Starting Up
2025-07-09 09:26:48.582 +08:00 [DBG] warn: 2025/7/9 09:26:48.582 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-09 09:26:48.746 +08:00 [DBG] info: 2025/7/9 09:26:48.746 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-09 09:26:48.751 +08:00 [INF] TopicService initialized and database ensured.
2025-07-09 09:26:48.926 +08:00 [INF] Initializing SemanticKernelService...
2025-07-09 09:26:48.944 +08:00 [INF] SemanticKernelService initialized.
2025-07-09 09:26:51.818 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-09 09:26:51.824 +08:00 [INF] Getting topics for user: llk
2025-07-09 09:26:52.346 +08:00 [DBG] info: 2025/7/9 09:26:52.346 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-09 09:26:52.407 +08:00 [INF] Getting messages for topic ID: 1
2025-07-09 09:26:52.423 +08:00 [DBG] info: 2025/7/9 09:26:52.423 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-09 09:26:52.447 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-09 09:26:52.448 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-09 09:27:40.226 +08:00 [INF] Application Shutting Down
2025-07-09 09:27:40.230 +08:00 [DBG] Hosting stopping
2025-07-09 09:27:40.231 +08:00 [INF] Application is shutting down...
2025-07-09 09:27:40.233 +08:00 [DBG] Hosting stopped
