﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System;
using System.Windows;
using Yidev.LocalAI.Models;
using Yidev.LocalAI.Services;
using Yidev.LocalAI.Services.Mcp;

namespace Yidev.LocalAI
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private IHost _host;

        protected override async void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            _host = Host.CreateDefaultBuilder()
                .UseSerilog((context, configuration) =>
                {
                    // The original logger config is moved here
                    configuration
                        .MinimumLevel.Debug()
                        .WriteTo.Debug()
                        .WriteTo.Console()
                        .WriteTo.File("logs/log-.txt", rollingInterval: RollingInterval.Day);
                })
                .ConfigureServices((context, services) =>
                {
                    // Register Core Services
                    services.AddSingleton<DatabaseContext>();
                    services.AddSingleton<TopicService>();
                    services.AddSingleton(provider =>
                    {
                        // This synchronously waits for the async creation,
                        // which is acceptable during app startup.
                        // 传递服务提供者以便 SemanticKernelService 可以获取插件
                        return SemanticKernelService.CreateAsync(provider).GetAwaiter().GetResult();
                    });

                    // Register ViewModels
                    services.AddSingleton<MainViewModel>();

                    // Register Windows
                    // This assumes the main window is called MainWindow
                    services.AddSingleton<MainWindow>();

                    // 注册你的插件 - 改为单例，因为 SemanticKernelService 是单例
                    services.AddSingleton<Yidev.LocalAI.McpPlugins.FileReaderPlugin>();
                    services.AddSingleton<Yidev.LocalAI.McpPlugins.FileWriterPlugin>();

                    // --- MCP Service Loading ---
                    // 1. Load services from the McpServices directory
                    services.AddMcpServices();

                    // 2. For demonstration, we also register the sample service directly.
                    // This ensures the service is available without requiring a separate DLL.
                    // 显式注册示例服务已不再需要，由 AddMcpServices 自动扫描并（按设置）完成注册。
                })
                .Build();

            await _host.StartAsync();

            Log.Information("Application Starting Up");

            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            mainWindow.Show();
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            Log.Information("Application Shutting Down");

            if (_host != null)
            {
                using (_host)
                {
                    await _host.StopAsync(TimeSpan.FromSeconds(5));
                }
            }

            Log.CloseAndFlush();
            base.OnExit(e);
        }
    }
}
